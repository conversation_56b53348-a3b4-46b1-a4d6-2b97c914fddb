"""
Shorts Editing Agent
-------------------
Interactive agent that edits and refines individual shorts based on user feedback.
"""

import os
import json
import logging
from typing import Dict

from langchain_openai import ChatOpenAI
from crewai import Agent, Task, Crew, Process
from crewai_tools import SerperDevTool, ScrapeWebsiteTool

from models.schema import Short, StructuredReport
from utils.parsers import ShortParser
from utils.content_moderation import sanitize_prompt, clean_narration_text

logger = logging.getLogger(__name__)


class ShortsEditingAgent:
    def __init__(self, verbose: bool = False, model: str = "gpt-4o-mini", provider: str = "openai"):
        """
        Initialize the shorts editing agent.

        Args:
            verbose (bool): Whether to enable verbose output from CrewAI.
                Defaults to False.
            model (str): LLM model to use. Defaults to "gpt-4o-mini".
            provider (str): LLM provider to use. Defaults to "openai".
        """
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.verbose = verbose
        self.model = model
        self.provider = provider

        if not self.openai_api_key:
            raise ValueError("Missing required API key for ShortsEditingAgent")

        # Initialize the LLM with higher temperature for creative editing
        self.llm = ChatOpenAI(
            model=self.model,
            temperature=0.8,
            api_key=self.openai_api_key
        )

    def edit_short(self, short: Short, feedback: str, structured_report: Dict) -> Short:
        """
        Edit a short based on user feedback.

        Args:
            short (Short): The short to edit
            feedback (str): User feedback for editing
            structured_report (Dict): Structured report for context

        Returns:
            Short: The edited short
        """
        logger.info(f"Editing short {short.short_number} based on feedback")

        # Create a parser for the Short model
        parser = ShortParser.create()
        format_instructions = parser.get_format_instructions()

        # Initialize search tools if available
        search_tools = []
        try:
            serper_tool = SerperDevTool()
            scrape_website_tool = ScrapeWebsiteTool()
            search_tools = [serper_tool, scrape_website_tool]
            logger.info("Search tools initialized for short editing")
        except Exception as e:
            logger.warning(f"Search tools not available: {str(e)}")

        # Create the editor agent
        editor = Agent(
            role="Hindi Short Content Editor",
            goal="Enhance short-form video content to maximize engagement, clarity, and emotional impact while maintaining factual accuracy",
            backstory="""You are a professional Hindi content editor specializing in short-form video content.
            You understand the unique requirements of shorts - quick hooks, compelling narratives, and strong 
            emotional payoffs within limited time constraints. Your expertise includes pacing optimization, 
            narrative structure for short attention spans, and creating content that works both as standalone 
            pieces and as part of a larger series. You maintain cultural authenticity while ensuring maximum 
            viewer engagement.""",
            verbose=self.verbose,
            allow_delegation=False,
            tools=search_tools,
            llm=self.llm
        )

        # Convert structured report to string for context
        structured_report_str = json.dumps(structured_report, ensure_ascii=False, indent=2)

        # Create the editing task
        editing_task = Task(
            description=f"""
            Edit the following Hindi short based on the user feedback:

            CURRENT SHORT:
            Short Number: {short.short_number}
            Title: {short.title}
            Narration: {short.narration}
            Estimated Duration: {short.estimated_duration_seconds} seconds
            Source Scenes: {short.source_scenes}

            STRUCTURED REPORT (for context):
            {structured_report_str}

            USER FEEDBACK:
            {feedback}

            Your task is to:

            1. **Apply User Feedback**: Carefully implement the requested changes while maintaining 
               the short's core narrative structure and appeal.

            2. **Optimize for Short-Form**: Ensure the content is optimized for short-form video:
               - Strong opening hook within first 3 seconds
               - Clear, concise storytelling
               - Emotional peaks and compelling moments
               - Satisfying conclusion or cliffhanger

            3. **Duration Management**: Keep the content within optimal duration:
               - Target: 20 seconds to 1 minute
               - Maximum: 2 minutes (only if absolutely necessary)
               - Ensure pacing matches the target duration

            4. **Maintain Quality**: Preserve the documentary-style Hindi narration quality:
               - Keep the conversational Hindi with natural English terms
               - Maintain factual accuracy and cultural authenticity
               - Ensure smooth narrative flow

            5. **Standalone Functionality**: Ensure the short can be understood independently:
               - Include necessary context for new viewers
               - Create clear narrative arc within the segment
               - End with resolution or compelling transition

            IMPORTANT:
            - Preserve the original Hindi language style and natural English term integration
            - Do not translate or modify the language mixing patterns
            - Maintain the documentary narrative voice
            - Keep factual accuracy intact
            - Focus on enhancing engagement and clarity

            Return the edited short with all fields properly filled.
            """,
            agent=editor,
            expected_output=format_instructions,
            llm=self.llm
        )

        # Create and run the crew
        crew = Crew(
            process=Process.sequential,
            tasks=[editing_task],
            agents=[editor],
            manager_llm=self.llm,
            verbose=self.verbose,
        )

        crew_output = crew.kickoff()
        result = crew_output.raw

        # Apply content moderation to the raw result before parsing
        sanitized_result = sanitize_prompt(result)

        # Parse the result using the Pydantic parser
        edited_short = ShortParser.parse_output(parser, sanitized_result)

        # If parsing fails, return the original short
        if edited_short is None:
            logger.warning(f"Could not parse Editor result for short {short.short_number}, Raw output: {result}")
            return short

        # Clean narration text to remove unwanted artifacts
        if edited_short.narration:
            edited_short.narration = clean_narration_text(edited_short.narration)

        logger.info(f"Short {short.short_number} editing completed successfully")
        return edited_short

    def save_short_to_json(self, short: Short, short_json_path: str) -> None:
        """
        Save the short to a JSON file.

        Args:
            short (Short): The short to save
            short_json_path (str): Path to the JSON file
        """
        try:
            with open(short_json_path, 'w', encoding='utf-8') as f:
                json.dump(short.model_dump(), f, ensure_ascii=False, indent=2)

            logger.info(f"Short saved to {short_json_path}")

        except Exception as e:
            logger.error(f"Error saving short to {short_json_path}: {str(e)}")
            print(f"\nError saving short: {str(e)}")

    def interactive_edit(self, short: Short, structured_report: Dict, short_json_path: str = None) -> Short:
        """
        Run an interactive editing session for the short.

        Args:
            short (Short): The short to edit
            structured_report (Dict): Structured report for context
            short_json_path (str, optional): Path to save the short JSON file after each edit

        Returns:
            Short: The edited short
        """
        edited_short = short

        print(f"\n=== Interactive Editing for Short {short.short_number}: {short.title} ===")
        print(f"Duration: {short.estimated_duration_seconds} seconds")
        print(f"Source Scenes: {short.source_scenes}")
        print("\nCurrent Narration:")
        print(f"{short.narration}")

        while True:
            print("\n" + "="*60)
            print("SHORT EDITING OPTIONS:")
            print("1. Edit short content")
            print("2. Change short title")
            print("3. Adjust duration estimate")
            print("4. Preview current short")
            print("5. Finish editing this short")
            print("="*60)

            choice = input("\nEnter your choice (1-5): ").strip()

            if choice == "1":
                # Edit short content
                feedback = input("\nProvide feedback for editing the short content: ").strip()
                if not feedback:
                    print("Feedback cannot be empty. Please try again.")
                    continue

                print("\nEditing short content based on your feedback. This may take a moment...")

                # Edit the short
                edited_short = self.edit_short(edited_short, feedback, structured_report)
                print(f"\nShort {edited_short.short_number} content has been updated.")

                # Save the short to JSON after the edit
                if short_json_path:
                    self.save_short_to_json(edited_short, short_json_path)
                    print(f"Short updated in {short_json_path}")

            elif choice == "2":
                # Change short title
                new_title = input(f"\nCurrent title: {edited_short.title}\nEnter new title: ").strip()
                if new_title:
                    edited_short.title = new_title
                    print(f"Title updated to: {new_title}")

                    # Save the short to JSON after the change
                    if short_json_path:
                        self.save_short_to_json(edited_short, short_json_path)
                        print(f"Short updated in {short_json_path}")

            elif choice == "3":
                # Adjust duration estimate
                try:
                    new_duration = int(input(f"\nCurrent duration: {edited_short.estimated_duration_seconds} seconds\nEnter new duration estimate (seconds): ").strip())
                    if 10 <= new_duration <= 300:  # Reasonable bounds
                        edited_short.estimated_duration_seconds = new_duration
                        print(f"Duration updated to: {new_duration} seconds")

                        # Save the short to JSON after the change
                        if short_json_path:
                            self.save_short_to_json(edited_short, short_json_path)
                            print(f"Short updated in {short_json_path}")
                    else:
                        print("Duration should be between 10 and 300 seconds.")
                except ValueError:
                    print("Invalid input. Please enter a number.")

            elif choice == "4":
                # Preview current short
                print(f"\n=== SHORT {edited_short.short_number} PREVIEW ===")
                print(f"Title: {edited_short.title}")
                print(f"Duration: {edited_short.estimated_duration_seconds} seconds")
                print(f"Source Scenes: {edited_short.source_scenes}")
                print(f"\nNarration:\n{edited_short.narration}")
                print("="*50)

            elif choice == "5":
                # Finish editing this short
                print(f"\nFinished editing Short {edited_short.short_number}")
                break

            else:
                print("Invalid choice. Please enter a number between 1 and 5.")

        return edited_short
