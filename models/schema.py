"""
Schema Models
------------
Pydantic models for structured data in the story generation pipeline.
"""

from pydantic import BaseModel, Field
from typing import List, Dict, Optional, Any





class Setting(BaseModel):
    """Setting description for a story location."""
    location: str = Field(description="Name or description of the location")
    description: str = Field(description="Detailed description of the location")
    atmosphere: str = Field(description="Atmosphere or mood of the location")


class ResearchData(BaseModel):
    """Research data structure for both real and fictional stories."""
    background_information: str = Field(description="Background information about the topic or story setting")
    key_events: List[str] = Field(default_factory=list, description="List of key events or plot points")
    cultural_context: str = Field(default="", description="Cultural context relevant to the story")
    potential_story_elements: List[str] = Field(default_factory=list, description="Potential elements to include in the story")
    possible_plot_twists: List[str] = Field(default_factory=list, description="Possible plot twists or unexpected developments")

    settings: List[Setting] = Field(default_factory=list, description="Settings or locations for the story")


class EnhancedResearchData(ResearchData):
    """Enhanced research data structure with additional narrative elements."""
    narrative_structure: str = Field(default="", description="Refined narrative structure and flow for the story")
    thematic_elements: List[str] = Field(default_factory=list, description="Key thematic elements and motifs in the story")
    emotional_arcs: List[str] = Field(default_factory=list, description="Emotional arcs and character development paths")
    dialogue_suggestions: List[str] = Field(default_factory=list, description="Suggested dialogue styles and key exchanges")
    cultural_authenticity_notes: str = Field(default="", description="Notes on ensuring cultural authenticity in the story")


class Scene(BaseModel):
    """A scene in the story."""
    scene_number: int = Field(
        description="Sequential number of the scene"
    )
    narration: str = Field(
        description="Hindi narration text for this scene (MUST be in Devanagari script)"
    )


class Story(BaseModel):
    """Complete story structure with scenes."""
    title: str = Field(
        description="Title of the story (can be in Hindi or English, depending on user input)"
    )
    scenes: List[Scene] = Field(
        description="List of scenes in the story"
    )


class SceneSegment(BaseModel):
    """A segment of a scene for narration."""
    scene_number: int = Field(
        description="Scene number this segment belongs to"
    )
    segment_number: int = Field(
        description="Sequential number of this segment within the scene"
    )
    narration: str = Field(
        description="Hindi narration text for this segment (MUST be in Devanagari script)"
    )
    estimated_duration_seconds: int = Field(
        description="Estimated duration of this segment in seconds",
        default=6
    )


class ImagePrompt(BaseModel):
    """Image prompt for a scene segment."""
    scene_number: int = Field(
        description="Scene number this image belongs to"
    )
    segment_number: int = Field(
        description="Segment number this image belongs to"
    )
    narration: str = Field(
        description="Hindi narration text for this segment"
    )
    image_prompt: str = Field(
        description="Detailed image prompt for image generation"
    )
    estimated_duration_seconds: int = Field(
        description="Estimated duration of this segment in seconds",
        default=6
    )


class SceneSegmentList(BaseModel):
    """A list of scene segments."""
    segments: List[SceneSegment] = Field(
        description="List of scene segments"
    )


class ImagePromptList(BaseModel):
    """A list of image prompts."""
    prompts: List[ImagePrompt] = Field(
        description="List of image prompts"
    )








# New models for Perplexity-based pipeline

class PerplexityReport(BaseModel):
    """Raw report data from Perplexity AI Sonar."""
    query: str = Field(
        description="The research query that was sent to Perplexity AI"
    )
    report_content: str = Field(
        description="Extracted report content (after </think> block)"
    )
    citations: List[str] = Field(
        default_factory=list,
        description="List of citation URLs from the research"
    )
    usage_stats: Dict[str, Any] = Field(
        default_factory=dict,
        description="API usage statistics from Perplexity"
    )


class StructuredReport(BaseModel):
    """Structured research report for documentary narration."""
    title: str = Field(
        description="Title of the incident/event being reported"
    )
    executive_summary: str = Field(
        description="Brief executive summary of the incident/event"
    )
    background_context: str = Field(
        description="Historical and contextual background information"
    )
    key_events_timeline: List[str] = Field(
        description="Chronological timeline of key events"
    )
    main_participants: List[str] = Field(
        description="Key people, organizations, or entities involved"
    )
    detailed_analysis: str = Field(
        description="In-depth analysis of the incident/event"
    )
    multiple_perspectives: List[str] = Field(
        description="Different viewpoints and perspectives on the incident"
    )
    impact_and_consequences: str = Field(
        description="Immediate and long-term impacts and consequences"
    )
    cultural_social_context: str = Field(
        description="Relevant cultural, social, and political context"
    )
    lessons_learned: str = Field(
        description="Key lessons learned or reforms implemented"
    )
    verified_sources: List[str] = Field(
        description="List of verified and credible sources used"
    )
    factual_accuracy_notes: str = Field(
        description="Notes on factual accuracy and verification"
    )


class TrendingKeyword(BaseModel):
    """Trending keyword data from DataForSEO."""
    keyword: str = Field(
        description="The trending keyword"
    )
    average_trend: float = Field(
        description="Average trend value over the analyzed period"
    )
    max_trend: int = Field(
        description="Maximum trend value recorded"
    )
    data_points: int = Field(
        description="Number of data points used for calculation"
    )


class VideoMetadata(BaseModel):
    """Video metadata including title, description, and hashtags."""
    video_title: str = Field(
        description="Optimized video title for YouTube/social media"
    )
    short_description: str = Field(
        description="Brief description of the video content"
    )
    detailed_description: str = Field(
        description="Detailed video description for platforms"
    )
    hashtags: List[str] = Field(
        description="List of relevant hashtags (25-30 hashtags)"
    )
    trending_keywords_used: List[str] = Field(
        description="List of trending keywords incorporated"
    )
    target_audience: str = Field(
        description="Description of the target audience"
    )
    content_category: str = Field(
        description="Content category (e.g., Documentary, News, Educational)"
    )
    estimated_watch_time: str = Field(
        description="Estimated watch time for the video"
    )
