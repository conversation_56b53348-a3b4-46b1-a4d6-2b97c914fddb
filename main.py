"""
AI Hindi Story Audio Generator
------------------------------
An autonomous Python-based agent that creates Hindi story audio content
based on real incidents. Generates story and audio files.
"""
import re
import os
import json
import glob
import logging
import argparse
import datetime
from typing import Optional, Tuple, List, Dict

# Import dotenv
from dotenv import load_dotenv

# Import agents
from agents.editor import EditorAgent
from agents.writer import WriterAgent
from agents.query_generator import QueryGenerationAgent
from agents.video_metadata import VideoMetadataAgent
from agents.story_editor import StoryEditorAgent
from agents.dialogue_splitter import DialogueSplitterAgent
from agents.query_editor import QueryEditingAgent
from agents.report_reviewer import ReportReviewAgent
from agents.structured_report_editor import StructuredReportEditingAgent
from agents.shorts_generator import ShortsGeneratorAgent
from agents.shorts_editing import ShortsEditingAgent
from agents.shorts_splitter import ShortsSplitterAgent

# Import inference modules
from inference.tts_generator import create_tts_generator
from inference.perplexity_client import PerplexityClient

# Import utilities
from utils.agent_factory import create_rate_limited_agent
from utils.shorts_utils import (get_shorts_directories, get_shorts_progress,
                                load_short_from_directory, count_completed_shorts,
                                load_segments_from_directory)

# Import schema models
from models.schema import (PerplexityReport, StructuredReport, ShortsMetadata,
                           Story, Scene, SceneSegment)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def setup_environment(llm_provider: str = "openai",
                      dev_mode: bool = True) -> None:
    """
    Load environment variables and create necessary directories.

    Args:
        llm_provider (str): The LLM provider to use. Either "openai" or "openrouter".
        dev_mode (bool): Whether to use development mode with local TTS.
            If True, ElevenLabs API key is not required.

    Raises:
        ValueError: If an unsupported provider is specified.
        EnvironmentError: If required API keys are missing.
    """
    # Force reload environment variables
    load_dotenv(override=True)

    # Define required API keys based on provider and dev mode
    required_keys = [
        'SERPER_API_KEY',
        'PERPLEXITY_API_KEY',
        'DATAFORSEO_API_KEY',
        'DATAFORSEO_API_LOGIN'
    ]

    # Only require ElevenLabs API key if not in dev mode
    if not dev_mode:
        required_keys.append('ELEVENLABS_API_KEY')
        logger.info("Production mode: ElevenLabs API key required")
    else:
        logger.info("Development mode: ElevenLabs API key not required")

    # Add LLM provider-specific API key requirement
    if llm_provider.lower() == "openai":
        required_keys.append('OPENAI_API_KEY')
    elif llm_provider.lower() == "openrouter":
        required_keys.append('OPENROUTER_API_KEY')
    elif llm_provider.lower() == "replicate":
        required_keys.append('REPLICATE_API_KEY')
    else:
        raise ValueError(f"Unsupported LLM provider: {llm_provider}. Use 'openai', 'openrouter', or 'replicate'.")



    missing_keys = [key for key in required_keys if not os.getenv(key)]
    if missing_keys:
        raise EnvironmentError(f"Missing required API keys: {', '.join(missing_keys)}")

    # Create the main assets directory if it doesn't exist
    os.makedirs('assets', exist_ok=True)

    logger.info("Environment setup complete")

def parse_arguments() -> argparse.Namespace:
    """
    Parse command line arguments for the AI Hindi Story Video Generator.

    Returns:
        argparse.Namespace: Parsed command line arguments containing all configuration options.

    Raises:
        SystemExit: If required arguments are missing or invalid arguments are provided.
    """
    parser = argparse.ArgumentParser(description='Generate AI Hindi story videos based on real incidents or fictional stories')

    # Story content arguments
    parser.add_argument('--title', help='Title of the story (required unless --continue is used)')
    parser.add_argument('--context', help='Additional context for the story (optional)')

    # Audio generation arguments
    parser.add_argument('--elevenlabs-voice-id', default='MaBqnF6LpI8cAT5sGihk',
                        help='ElevenLabs voice ID to use for narration (default: MaBqnF6LpI8cAT5sGihk)')
    parser.add_argument('--dev', action='store_true', default=True,
                        help='Use development mode with local Coqui-AI TTS instead of ElevenLabs (default: True)')
    parser.add_argument('--no-dev', dest='dev', action='store_false',
                        help='Use production mode with ElevenLabs TTS instead of local Coqui-AI TTS')

    # LLM model arguments
    parser.add_argument('--model', default='gpt-4o-mini',
                        help='LLM model to use for all agents (default: gpt-4o-mini)')
    parser.add_argument('--provider', choices=['openai', 'openrouter', 'replicate'], default='openai',
                        help='LLM provider to use (default: openai)')
    parser.add_argument('--max-tokens-per-minute', dest='max_tokens_per_minute', type=int, default=30000,
                        help='Maximum tokens per minute for OpenAI API rate limiting (default: 30000)')

    # Agent-specific LLM model arguments
    parser.add_argument('--query-generator-model', dest='query_generator_model',
                        help='LLM model to use for QueryGenerationAgent (defaults to --model)')
    parser.add_argument('--query-generator-provider', dest='query_generator_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for QueryGenerationAgent (defaults to --provider)')

    parser.add_argument('--editor-model', dest='editor_model',
                        help='LLM model to use for EditorAgent (defaults to --model)')
    parser.add_argument('--editor-provider', dest='editor_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for EditorAgent (defaults to --provider)')

    parser.add_argument('--writer-model', dest='writer_model',
                        help='LLM model to use for WriterAgent (defaults to --model)')
    parser.add_argument('--writer-provider', dest='writer_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for WriterAgent (defaults to --provider)')

    parser.add_argument('--video-metadata-model', dest='video_metadata_model',
                        help='LLM model to use for VideoMetadataAgent (defaults to --model)')
    parser.add_argument('--video-metadata-provider', dest='video_metadata_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for VideoMetadataAgent (defaults to --provider)')

    parser.add_argument('--dialogue-splitter-model', dest='dialogue_splitter_model',
                        help='LLM model to use for DialogueSplitterAgent (defaults to --model)')
    parser.add_argument('--dialogue-splitter-provider', dest='dialogue_splitter_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for DialogueSplitterAgent (defaults to --provider)')

    parser.add_argument('--story-editor-model', dest='story_editor_model',
                        help='LLM model to use for StoryEditorAgent (defaults to --model)')
    parser.add_argument('--story-editor-provider', dest='story_editor_provider', choices=['openai', 'openrouter', 'replicate'],
                        help='LLM provider to use for StoryEditorAgent (defaults to --provider)')



    # Research configuration arguments
    parser.add_argument('--reasoning-effort', choices=['low', 'medium', 'high'], default='low',
                        help='Reasoning effort level for Perplexity AI research (default: low)')

    # Debug and logging arguments
    parser.add_argument('--verbose', action='store_true', default=False,
                        help='Enable verbose output from CrewAI agents (default: False)')
    parser.add_argument('--interactive-editing', action='store_true', default=True,
                        help='Enable interactive story editing workflow (default: True)')

    # Video output options
    parser.add_argument('--create-fast-version', action='store_true', default=True,
                        help='Create an additional 2x speed video output for quick review (default: True)')

    # Continuation options
    parser.add_argument('--continue', dest='continue_generation', nargs='?', const=True, default=False,
                        help='Continue video generation from where it left off. Can be used with a path to a specific story directory.')

    args = parser.parse_args()

    # Skip validation if using --continue flag
    if not args.continue_generation:
        # Validate that title is provided when not using --continue
        if not args.title:
            parser.error('--title is required when not using --continue')

    return args


def get_agent_config(args, agent_name: str, global_model: str, global_provider: str) -> Tuple[str, str]:
    """
    Get agent-specific model and provider configuration with fallback to global values.

    Args:
        args: Parsed command line arguments
        agent_name: Name of the agent (e.g., 'researcher', 'editor', etc.)
        global_model: Global model to use as fallback
        global_provider: Global provider to use as fallback

    Returns:
        Tuple[str, str]: (model, provider) for the specific agent
    """
    # Convert agent name to attribute format (e.g., 'researcher' -> 'researcher_model')
    model_attr = f"{agent_name}_model"
    provider_attr = f"{agent_name}_provider"

    # Get agent-specific values or fall back to global values
    agent_model = getattr(args, model_attr, None) or global_model
    agent_provider = getattr(args, provider_attr, None) or global_provider

    return agent_model, agent_provider


def get_latest_story_dir() -> Optional[str]:
    """
    Get the most recently created story directory.

    Returns:
        Optional[str]: Path to the most recent story directory, or None if no directories exist.
    """
    assets_dir = 'assets'
    if not os.path.exists(assets_dir):
        return None

    # Get all subdirectories in the assets directory
    subdirs = [d for d in os.listdir(assets_dir) if os.path.isdir(os.path.join(assets_dir, d))]
    if not subdirs:
        return None

    # Sort by creation time (newest first)
    subdirs.sort(key=lambda d: os.path.getctime(os.path.join(assets_dir, d)), reverse=True)

    # Return the newest directory
    return os.path.join(assets_dir, subdirs[0])


def get_existing_audio_files(story_dir: str) -> List[str]:
    """
    Get a list of existing audio files in the story directory.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        List[str]: List of audio file paths
    """
    audio_dir = os.path.join(story_dir, 'audio')
    if not os.path.exists(audio_dir):
        return []

    # Get all MP3 files in the audio directory
    audio_files = glob.glob(os.path.join(audio_dir, '*.mp3'))

    # Sort the files by scene and segment number
    def extract_numbers(path):
        # Extract scene and segment numbers from the filename
        match = re.search(r'scene_(\d+)_segment_(\d+)', path)
        if match:
            return (int(match.group(1)), int(match.group(2)))
        return (0, 0)

    audio_files.sort(key=extract_numbers)
    return audio_files





def get_pipeline_progress(story_dir: str) -> Dict[str, bool]:
    """
    Determine which steps of the pipeline have been completed based on existing JSON files.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        Dict[str, bool]: Dictionary indicating which steps are complete
    """
    progress = {
        'query_generated': False,
        'research_report': False,
        'structured_report': False,
        'story_written': False,
        'dialogue_segments': False,
        'video_metadata': False,
        'audio_generated': False,
        'shorts_generated': False,
        'shorts_edited': False,
        'shorts_split': False,
        'shorts_audio_generated': False,
        'shorts_metadata_generated': False
    }

    # Check for query generation (stored in query.md)
    query_path = os.path.join(story_dir, 'query.md')
    if os.path.exists(query_path):
        progress['query_generated'] = True

    # Check for research report (stored in report.json)
    report_path = os.path.join(story_dir, 'report.json')
    if os.path.exists(report_path):
        progress['research_report'] = True

    # Check for structured report
    structured_report_path = os.path.join(story_dir, 'structured_report.json')
    if os.path.exists(structured_report_path):
        progress['structured_report'] = True

    # Check for story
    story_path = os.path.join(story_dir, 'story.json')
    if os.path.exists(story_path):
        progress['story_written'] = True



    # Check for dialogue segments
    dialogue_path = os.path.join(story_dir, 'dialogue_segments.json')
    if os.path.exists(dialogue_path):
        progress['dialogue_segments'] = True

    # Check for video metadata
    metadata_path = os.path.join(story_dir, 'video_metadata.json')
    if os.path.exists(metadata_path):
        progress['video_metadata'] = True

    # Check for audio files
    audio_dir = os.path.join(story_dir, 'audio')
    if os.path.exists(audio_dir):
        audio_files = glob.glob(os.path.join(audio_dir, '*.mp3'))
        if audio_files:
            progress['audio_generated'] = True

    # Check for shorts pipeline progress
    short_dirs = get_shorts_directories(story_dir)
    if short_dirs:
        shorts_progress = get_shorts_progress(story_dir)

        # Check if shorts are generated (at least one short.json exists)
        if any(sp['short_generated'] for sp in shorts_progress.values()):
            progress['shorts_generated'] = True

        # Check if shorts are edited (all existing shorts have been processed)
        # For now, we'll assume editing is complete if shorts are generated
        # This can be enhanced later with explicit editing tracking
        if progress['shorts_generated']:
            progress['shorts_edited'] = True

        # Check if shorts are split (at least one segments.json exists)
        if any(sp['segments_generated'] for sp in shorts_progress.values()):
            progress['shorts_split'] = True

        # Check if shorts audio is generated (at least one short has audio)
        if any(sp['audio_generated'] for sp in shorts_progress.values()):
            progress['shorts_audio_generated'] = True

        # Check if shorts metadata is generated (at least one metadata.json exists)
        if any(sp['metadata_generated'] for sp in shorts_progress.values()):
            progress['shorts_metadata_generated'] = True

    return progress


def validate_story_directory(story_dir: str) -> Optional[str]:
    """
    Validate that a story directory can be used for continuation.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        Optional[str]: Validated story directory path or None if invalid
    """
    # Check if the directory exists
    if not os.path.exists(story_dir):
        logger.error(f"Directory does not exist: {story_dir}")
        return None

    # Get pipeline progress to determine if we can continue
    progress = get_pipeline_progress(story_dir)

    # Check if at least one step has been completed
    if not any(progress.values()):
        logger.error(f"No pipeline progress found in directory: {story_dir}")
        logger.error("Directory must contain at least one of: query.md, report.json, structured_report.json, story.json, dialogue_segments.json, video_metadata.json, or audio files")
        return None

    # Create subdirectories if they don't exist
    os.makedirs(os.path.join(story_dir, 'audio'), exist_ok=True)

    # Log the current progress
    completed_steps = [step for step, completed in progress.items() if completed]
    logger.info(f"Found completed pipeline steps: {', '.join(completed_steps)}")

    return story_dir


def extract_title_from_existing_data(story_dir: str) -> Optional[str]:
    """
    Extract title from existing data files in the story directory.

    Args:
        story_dir (str): Path to the story directory

    Returns:
        Optional[str]: Extracted title or None if not found
    """
    # Try to get title from story.json first
    story_path = os.path.join(story_dir, 'story.json')
    if os.path.exists(story_path):
        try:
            with open(story_path, 'r', encoding='utf-8') as f:
                story_data = json.load(f)
                return story_data.get('title')
        except Exception as e:
            logger.debug(f"Could not read title from story.json: {e}")

    # Try to get title from structured_report.json
    structured_report_path = os.path.join(story_dir, 'structured_report.json')
    if os.path.exists(structured_report_path):
        try:
            with open(structured_report_path, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
                return report_data.get('title')
        except Exception as e:
            logger.debug(f"Could not read title from structured_report.json: {e}")

    # Try to extract title from directory name as last resort
    dir_name = os.path.basename(story_dir)
    # Remove timestamp suffix (format: title_YYYYMMDD_HHMMSS)
    title_match = re.match(r'^(.+)_\d{8}_\d{6}$', dir_name)
    if title_match:
        # Convert underscores back to spaces and title case
        title = title_match.group(1).replace('_', ' ').title()
        logger.debug(f"Extracted title from directory name: {title}")
        return title

    return None


def create_story_directory(title: str) -> str:
    """
    Create a unique directory for the story assets.

    Args:
        title (str): The title of the story to create a directory for.

    Returns:
        str: Path to the created story directory.
    """
    # Create a timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

    # Create a safe directory name from the title
    safe_title = re.sub(r'[^\w\s-]', '', title).strip().lower()
    safe_title = re.sub(r'[-\s]+', '_', safe_title)

    # Combine title and timestamp
    dir_name = f"{safe_title}_{timestamp}"

    # Create the full path
    story_dir = os.path.join('assets', dir_name)

    # Create the directory and subdirectories
    os.makedirs(story_dir, exist_ok=True)
    os.makedirs(os.path.join(story_dir, 'audio'), exist_ok=True)


    logger.info(f"Created story directory: {story_dir}")
    return story_dir


def main() -> Optional[str]:
    """
    Main execution function for the AI Hindi Story Audio Generator.

    This function orchestrates the entire audio generation pipeline including:
    - Environment setup and argument parsing
    - Story research and generation
    - Audio generation

    Returns:
        Optional[str]: Path to the story directory containing generated assets, or None on error.
    """
    # Parse arguments
    args = parse_arguments()

    # Extract core arguments for environment setup
    llm_provider = args.provider
    dev_mode = args.dev

    # Setup environment with all relevant providers and dev mode
    setup_environment(llm_provider=llm_provider, dev_mode=dev_mode)

    # Extract other arguments
    title = args.title
    context = args.context or ""

    elevenlabs_voice_id = args.elevenlabs_voice_id
    verbose = args.verbose
    interactive_editing = args.interactive_editing
    model = args.model
    max_tokens_per_minute = args.max_tokens_per_minute
    create_fast_version = args.create_fast_version
    continue_generation = args.continue_generation
    reasoning_effort = args.reasoning_effort

    # Handle the continue flag
    story_dir = None

    if continue_generation:
        # If a path is provided with --continue
        if isinstance(continue_generation, str):
            # Use the provided path
            story_dir = continue_generation
            logger.info(f"Attempting to continue generation from specified directory: {story_dir}")

        else:
            # Find the most recent story directory
            story_dir = get_latest_story_dir()
            if story_dir:
                logger.info(f"Attempting to continue generation from most recent directory: {story_dir}")
            else:
                logger.error("No existing story directories found to continue from.")
                return None

        # Validate the story directory
        story_dir = validate_story_directory(story_dir)

        if not story_dir:
            logger.error("Cannot continue generation. Exiting.")
            return None

        # Log that we're continuing from an existing directory
        logger.info(f"Continuing video generation from: {story_dir}")

        # If title is not provided when continuing, try to extract it from existing data
        if not title:
            title = extract_title_from_existing_data(story_dir)
            if not title:
                logger.error("Could not determine title from existing data and no title provided")
                return None
            logger.info(f"Extracted title from existing data: '{title}'")

    else:
        # Log normal generation parameters
        logger.info(f"Starting real story generation for title: '{title}'")


        logger.info(f"TTS Provider: {'Coqui-AI (local)' if args.dev else f'ElevenLabs (voice ID: {elevenlabs_voice_id})'}")
        logger.info(f"Using LLM provider: {llm_provider}")
        logger.info(f"Using LLM model: {model}")
        logger.info(f"CrewAI verbose mode: {'Enabled' if verbose else 'Disabled'}")
        logger.info(f"Interactive editing mode: {'Enabled' if interactive_editing else 'Disabled'}")
        logger.info(f"2x speed video creation: {'Enabled' if create_fast_version else 'Disabled'}")

        # Create a new directory for this story
        story_dir = create_story_directory(title)

    # Get pipeline progress to determine what steps to skip
    progress = get_pipeline_progress(story_dir)

    # Initialize variables for pipeline data
    story: Optional[Story] = None
    perplexity_report: Optional[PerplexityReport] = None
    structured_report: Optional[StructuredReport] = None

    # Define paths for all assets
    story_json_path = os.path.join(story_dir, 'story.json')
    query_md_path = os.path.join(story_dir, 'query.md')
    report_json_path = os.path.join(story_dir, 'report.json')
    structured_report_json_path = os.path.join(story_dir, 'structured_report.json')

    # Load existing data if available
    if progress['story_written']:
        logger.info("Loading existing story data...")
        try:
            with open(story_json_path, 'r', encoding='utf-8') as f:
                story_data = json.load(f)
                story = Story(**story_data)
            logger.info(f"Loaded existing story with {len(story.scenes)} scenes")
        except Exception as e:
            logger.error(f"Error reading existing story: {str(e)}")
            progress['story_written'] = False

    if progress['research_report']:
        logger.info("Loading existing research report...")
        try:
            with open(report_json_path, 'r', encoding='utf-8') as f:
                report_data = json.load(f)
                perplexity_report = PerplexityReport(**report_data)
            logger.info("Loaded existing research report")
        except Exception as e:
            logger.error(f"Error reading existing research report: {str(e)}")
            progress['research_report'] = False

    if progress['structured_report']:
        logger.info("Loading existing structured report...")
        try:
            with open(structured_report_json_path, 'r', encoding='utf-8') as f:
                structured_data = json.load(f)
                structured_report = StructuredReport(**structured_data)
            logger.info("Loaded existing structured report")
        except Exception as e:
            logger.error(f"Error reading existing structured report: {str(e)}")
            progress['structured_report'] = False

    # Step 1: Generate research query (if not already done)
    research_query = None
    if not progress['query_generated']:
        logger.info("Step 1: Generating research query")

        # Get agent-specific configuration
        query_generator_model, query_generator_provider = get_agent_config(args, 'query_generator', model, llm_provider)
        logger.info(f"QueryGenerationAgent using model: {query_generator_model}, provider: {query_generator_provider}")

        query_generator = QueryGenerationAgent(
            verbose=verbose,
            model=query_generator_model,
            provider=query_generator_provider
        )

        research_query = query_generator.generate_research_query(title, context)
        logger.info("Research query generated successfully")

        # Save initial query to markdown file (before editing)
        query_generator.save_query_to_file(query_md_path, research_query, title, context, is_edited=False)
        logger.info(f"Initial research query saved to: {query_md_path}")

        # Interactive query editing (if enabled)
        if interactive_editing:
            logger.info("Starting interactive query editing workflow")

            query_editor = QueryEditingAgent(
                verbose=verbose,
                model=query_generator_model,
                provider=query_generator_provider
            )

            research_query = query_editor.interactive_edit(
                research_query,
                title,
                context,
                query_file_path=query_md_path
            )

            logger.info("Query editing complete")

        progress['query_generated'] = True
    else:
        logger.info("Step 1: Skipping query generation (already completed)")
        # Load existing query from file
        try:
            with open(query_md_path, 'r', encoding='utf-8') as f:
                content = f.read()
                # Try to extract edited query first, then fall back to original
                edited_query_start = content.find("**Generated Query (Edited):**")
                if edited_query_start != -1:
                    research_query = content[edited_query_start + len("**Generated Query (Edited):**"):].strip()
                    logger.info("Loaded edited research query from file")
                else:
                    # Extract original query from markdown
                    query_start = content.find("**Generated Query:**")
                    if query_start != -1:
                        research_query = content[query_start + len("**Generated Query:**"):].strip()
                        logger.info("Loaded original research query from file")
                    else:
                        # Fallback: use entire content if format is different
                        research_query = content.strip()
                        logger.info("Loaded research query from file (fallback format)")

                # Remove any leading newlines
                research_query = research_query.lstrip('\n')
        except Exception as e:
            logger.error(f"Error loading query from {query_md_path}: {e}")
            return None

    # Step 2: Generate research report using Perplexity AI Sonar (if not already done)
    if not progress['research_report']:
        logger.info("Step 2: Generating research report using Perplexity AI Sonar")

        if not research_query:
            logger.error("Research query is required but not available")
            return None

        # Generate research report using Perplexity AI Sonar
        perplexity_client = PerplexityClient()
        perplexity_response = perplexity_client.generate_research_report(research_query, reasoning_effort=reasoning_effort)
        report_content = perplexity_client.extract_report_content(perplexity_response)

        # Create PerplexityReport object
        perplexity_report = PerplexityReport(
            query=research_query,
            report_content=report_content,
            citations=perplexity_response.get("citations", []),
            usage_stats=perplexity_response.get("usage", {})
        )

        # Save PerplexityReport to JSON
        perplexity_client.save_report(perplexity_report.model_dump(), report_json_path)
        logger.info(f"PerplexityReport saved to {report_json_path}")

        # Interactive in-depth report review (if enabled)
        if interactive_editing:
            logger.info("Starting interactive in-depth report review")

            report_reviewer = ReportReviewAgent()
            should_continue = report_reviewer.interactive_review(perplexity_report, title)

            if not should_continue:
                logger.info("Pipeline stopped for manual report editing")
                print("\nPipeline stopped. You can manually edit the report.json file and use --continue to resume.")
                print("Note: The research query is available in query.md for reference.")
                return None

            logger.info("In-depth report review complete, continuing with pipeline")

        progress['research_report'] = True

    else:
        logger.info("Step 2: Skipping research report generation (already completed)")

    # Step 3: Structure the report using EditorAgent (if not already done)
    if not progress['structured_report']:
        logger.info("Step 3: Structuring research report for documentary narration")

        # Get agent-specific configuration
        editor_model, editor_provider = get_agent_config(args, 'editor', model, llm_provider)
        logger.info(f"EditorAgent using model: {editor_model}, provider: {editor_provider}")

        editor_agent = EditorAgent(verbose=verbose, model=editor_model, provider=editor_provider)
        structured_report = editor_agent.structure_report(perplexity_report, title)

        # Save structured report to JSON immediately after generation
        with open(structured_report_json_path, 'w', encoding='utf-8') as f:
            json.dump(structured_report.model_dump(), f, ensure_ascii=False, indent=4)

        logger.info(f"Structured report saved to {structured_report_json_path}")

        # Interactive structured report editing (if enabled)
        if interactive_editing:
            logger.info("Starting interactive structured report editing workflow")

            structured_report_editor = StructuredReportEditingAgent(
                verbose=verbose,
                model=editor_model,
                provider=editor_provider
            )

            # Pass the structured_report_json_path to enable real-time updates during editing
            structured_report = structured_report_editor.interactive_edit(structured_report, perplexity_report, structured_report_json_path)

            # Final save to ensure consistency
            with open(structured_report_json_path, 'w', encoding='utf-8') as f:
                json.dump(structured_report.model_dump(), f, ensure_ascii=False, indent=4)

            logger.info("Structured report editing complete")
            print("Interactive editing complete. Proceeding to next steps...")

        progress['structured_report'] = True
    else:
        logger.info("Step 3: Skipping report structuring (already completed)")

    # Step 4: Create documentary narration using WriterAgent (if not already done)
    if not progress['story_written']:
        logger.info("Step 4: Creating Hindi documentary-style narration")

        # Get agent-specific configuration
        writer_model, writer_provider = get_agent_config(args, 'writer', model, llm_provider)
        logger.info(f"WriterAgent using model: {writer_model}, provider: {writer_provider}")

        writer_agent = WriterAgent(verbose=verbose, model=writer_model, provider=writer_provider)
        story = writer_agent.write_documentary_story(structured_report, title)

        # Save story to JSON
        with open(story_json_path, 'w', encoding='utf-8') as f:
            json.dump(story.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Documentary story written and saved to {story_json_path}")
        progress['story_written'] = True
    else:
        logger.info("Step 4: Skipping story writing (already completed)")

    # Log the story structure
    logger.info(f"Story has {len(story.scenes)} scenes")

    # Step 5: Interactive story editing (if enabled)
    if interactive_editing:
        logger.info("Starting interactive story editing workflow")

        # Get agent-specific configuration
        story_editor_model, story_editor_provider = get_agent_config(args, 'story_editor', model, llm_provider)
        logger.info(f"StoryEditorAgent using model: {story_editor_model}, provider: {story_editor_provider}")

        editor = create_rate_limited_agent(
            role="Story Editor",
            goal="Edit and refine the story based on user feedback",
            backstory="You are an expert story editor who helps refine and improve stories.",
            model=story_editor_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=story_editor_provider
        )

        story_editor = StoryEditorAgent(verbose=verbose, model=story_editor_model, provider=story_editor_provider)
        story_editor.llm = editor.llm  # Use the rate-limited LLM

        # Pass the story_json_path to enable real-time updates during editing
        story = story_editor.interactive_edit(story, structured_report, story_json_path)

        # Final save is still needed to ensure consistency
        with open(story_json_path, 'w', encoding='utf-8') as f:
            json.dump(story.model_dump(), f, ensure_ascii=False, indent=2)

        logger.info(f"Edited story saved to {story_json_path}")
        print("Interactive editing complete. Proceeding to shorts generation...")

    # Step 6: Generate shorts from the complete story (if not already done)
    if not progress['shorts_generated']:
        logger.info("Step 6: Generating shorts from the complete story")

        # Get agent-specific configuration
        shorts_generator_model, shorts_generator_provider = get_agent_config(args, 'shorts_generator', model, llm_provider)
        logger.info(f"ShortsGeneratorAgent using model: {shorts_generator_model}, provider: {shorts_generator_provider}")

        shorts_generator = ShortsGeneratorAgent(verbose=verbose, model=shorts_generator_model, provider=shorts_generator_provider)
        shorts = shorts_generator.generate_shorts(story)

        # Save shorts to individual directories
        shorts_generator.save_shorts_to_directories(shorts, story_dir)

        logger.info(f"Generated {len(shorts)} shorts and saved to individual directories")
        progress['shorts_generated'] = True
    else:
        logger.info("Step 6: Skipping shorts generation (already completed)")

    # Step 7: Interactive shorts editing (if enabled)
    if interactive_editing and progress['shorts_generated']:
        logger.info("Step 7: Starting interactive shorts editing workflow")

        # Get agent-specific configuration
        shorts_editing_model, shorts_editing_provider = get_agent_config(args, 'shorts_editing', model, llm_provider)
        logger.info(f"ShortsEditingAgent using model: {shorts_editing_model}, provider: {shorts_editing_provider}")

        shorts_editing_agent = ShortsEditingAgent(verbose=verbose, model=shorts_editing_model, provider=shorts_editing_provider)

        # Process each short individually
        short_dirs = get_shorts_directories(story_dir)

        for short_dir in short_dirs:
            short = load_short_from_directory(short_dir)
            if short is None:
                logger.warning(f"Could not load short from {short_dir}, skipping")
                continue

            short_json_path = os.path.join(short_dir, "short.json")
            edited_short = shorts_editing_agent.interactive_edit(short, structured_report, short_json_path)

            # Final save to ensure consistency
            shorts_editing_agent.save_short_to_json(edited_short, short_json_path)

        logger.info("Interactive shorts editing complete")
        progress['shorts_edited'] = True
    else:
        if not interactive_editing:
            logger.info("Step 7: Skipping shorts editing (interactive editing disabled)")
            progress['shorts_edited'] = True
        else:
            logger.info("Step 7: Skipping shorts editing (shorts not generated yet)")

    # Step 8: Split shorts into segments for text-to-video (if not already done)
    if not progress['shorts_split'] and progress['shorts_edited']:
        logger.info("Step 8: Splitting shorts into segments for text-to-video generation")

        # Get agent-specific configuration
        shorts_splitter_model, shorts_splitter_provider = get_agent_config(args, 'shorts_splitter', model, llm_provider)
        logger.info(f"ShortsSplitterAgent using model: {shorts_splitter_model}, provider: {shorts_splitter_provider}")

        shorts_splitter = ShortsSplitterAgent(verbose=verbose, model=shorts_splitter_model, provider=shorts_splitter_provider)
        shorts_splitter.process_all_shorts(story_dir)

        logger.info("Shorts splitting completed")
        progress['shorts_split'] = True
    else:
        if not progress['shorts_edited']:
            logger.info("Step 8: Skipping shorts splitting (shorts not edited yet)")
        else:
            logger.info("Step 8: Skipping shorts splitting (already completed)")

    # Step 9: Split dialogue for scenes (if not already done)
    dialogue_segments_path = os.path.join(story_dir, 'dialogue_segments.json')
    scenes = None

    if progress['dialogue_segments']:
        logger.info("Step 9: Loading existing dialogue segments...")
        try:
            with open(dialogue_segments_path, 'r', encoding='utf-8') as f:
                segments_data = json.load(f)
                # Reconstruct scenes from the saved data
                scenes = [SceneSegment(**segment) for segment in segments_data]
            logger.info(f"Loaded {len(scenes)} dialogue segments")
        except Exception as e:
            logger.error(f"Error reading existing dialogue segments: {str(e)}")
            progress['dialogue_segments'] = False

    if not progress['dialogue_segments']:
        logger.info("Step 9: Splitting dialogue into segments for TTS")

        # Get agent-specific configuration
        dialogue_splitter_model, dialogue_splitter_provider = get_agent_config(args, 'dialogue_splitter', model, llm_provider)
        logger.info(f"DialogueSplitterAgent using model: {dialogue_splitter_model}, provider: {dialogue_splitter_provider}")

        splitter = create_rate_limited_agent(
            role="Dialogue Splitter",
            goal="Split Hindi narration into optimal segments for TTS and image generation",
            backstory="You are an expert in audio-visual storytelling who optimizes content for narration and visuals.",
            model=dialogue_splitter_model,
            max_tokens_per_minute=max_tokens_per_minute,
            verbose=verbose,
            provider=dialogue_splitter_provider
        )

        dialogue_splitter = DialogueSplitterAgent(verbose=verbose, model=dialogue_splitter_model, provider=dialogue_splitter_provider)
        dialogue_splitter.llm = splitter.llm  # Use the rate-limited LLM
        scenes = dialogue_splitter.split(story)

        # Save dialogue segments to JSON
        with open(dialogue_segments_path, 'w', encoding='utf-8') as f:
            segments_data = [segment.model_dump() for segment in scenes]
            json.dump(segments_data, f, ensure_ascii=False, indent=2)
        logger.info(f"Dialogue segments saved to: {dialogue_segments_path}")
        progress['dialogue_segments'] = True
    else:
        logger.info("Step 9: Skipping dialogue splitting (already completed)")

    # Step 10: Generate TTS narration (if not already done)
    audio_paths = []

    if progress['audio_generated']:
        logger.info("Step 10: Loading existing audio files...")
        audio_paths = get_existing_audio_files(story_dir)
        logger.info(f"Found {len(audio_paths)} existing audio files")

    if not progress['audio_generated'] or len(audio_paths) < len(scenes):
        remaining_scenes_count = len(scenes) - len(audio_paths) if progress['audio_generated'] else len(scenes)
        logger.info(f"Step 10: Generating audio for {remaining_scenes_count} scenes")

        # Use the TTS generator factory to select the appropriate generator based on dev mode
        dev_mode = args.dev
        tts_generator = create_tts_generator(
            dev_mode=dev_mode,
            voice_id=elevenlabs_voice_id
        )

        # Log which TTS provider is being used
        if dev_mode:
            logger.info("Using espeak-ng TTS for local development (--dev=True)")
        else:
            logger.info(f"Using ElevenLabs TTS with voice ID: {elevenlabs_voice_id} (--dev=False)")

        # If continuing, determine which scenes need audio generation
        if progress['audio_generated'] and audio_paths:
            # Get the highest scene and segment numbers from existing files
            max_scene = 0
            max_segment = 0

            for audio_file in audio_paths:
                match = re.search(r'scene_(\d+)_segment_(\d+)', audio_file)
                if match:
                    scene_num = int(match.group(1))
                    segment_num = int(match.group(2))
                    if scene_num > max_scene or (scene_num == max_scene and segment_num > max_segment):
                        max_scene = scene_num
                        max_segment = segment_num

            # Filter scenes to only include those that need generation
            remaining_scenes = [
                scene for scene in scenes
                if scene.scene_number > max_scene or
                (scene.scene_number == max_scene and scene.segment_number > max_segment)
            ]

            # Generate audio for remaining scenes
            new_audio_paths = tts_generator.generate_audio(remaining_scenes, os.path.join(story_dir, 'audio'))
            audio_paths = audio_paths + new_audio_paths

        else:
            # Generate audio for all scenes
            audio_paths = tts_generator.generate_audio(scenes, os.path.join(story_dir, 'audio'))

        progress['audio_generated'] = True
    else:
        logger.info("Step 7: Skipping audio generation (already completed)")

    # Log audio files
    logger.info(f"Total audio files: {len(audio_paths)}")

    # Step 11: Generate audio for shorts segments (if not already done)
    if not progress['shorts_audio_generated'] and progress['shorts_split']:
        logger.info("Step 11: Generating audio for shorts segments")

        # Use the same TTS generator as for regular audio
        dev_mode = args.dev
        tts_generator = create_tts_generator(
            dev_mode=dev_mode,
            voice_id=elevenlabs_voice_id
        )

        short_dirs = get_shorts_directories(story_dir)

        for short_dir in short_dirs:
            segments = load_segments_from_directory(short_dir)
            if segments is None:
                logger.warning(f"Could not load segments from {short_dir}, skipping")
                continue

            # Check if audio already exists for this short
            audio_dir = os.path.join(short_dir, 'audio')
            existing_audio = []
            if os.path.exists(audio_dir):
                existing_audio = [f for f in os.listdir(audio_dir) if f.endswith(('.mp3', '.wav'))]

            if len(existing_audio) >= len(segments):
                logger.info(f"Audio already exists for short {os.path.basename(short_dir)}, skipping")
                continue

            logger.info(f"Generating audio for short {os.path.basename(short_dir)} ({len(segments)} segments)")

            # Generate audio for this short's segments
            short_audio_paths = tts_generator.generate_audio(segments, audio_dir)
            logger.info(f"Generated {len(short_audio_paths)} audio files for short {os.path.basename(short_dir)}")

        progress['shorts_audio_generated'] = True
        logger.info("Shorts audio generation completed")
    else:
        if not progress['shorts_split']:
            logger.info("Step 11: Skipping shorts audio generation (shorts not split yet)")
        else:
            logger.info("Step 11: Skipping shorts audio generation (already completed)")

    # Step 12: Generate video metadata (if not already done)
    video_metadata_json_path = os.path.join(story_dir, 'video_metadata.json')

    if not progress['video_metadata']:
        logger.info("Step 12: Generating video metadata with trending keywords")

        # Get agent-specific configuration
        video_metadata_model, video_metadata_provider = get_agent_config(args, 'video_metadata', model, llm_provider)
        logger.info(f"VideoMetadataAgent using model: {video_metadata_model}, provider: {video_metadata_provider}")

        try:
            video_metadata_agent = VideoMetadataAgent(
                verbose=verbose,
                model=video_metadata_model,
                provider=video_metadata_provider
            )

            video_metadata = video_metadata_agent.generate_video_metadata(story)

            # Save video metadata to JSON
            with open(video_metadata_json_path, 'w', encoding='utf-8') as f:
                json.dump(video_metadata.model_dump(), f, ensure_ascii=False, indent=2)

            logger.info(f"Video metadata saved to {video_metadata_json_path}")
            logger.info(f"Generated video title: {video_metadata.video_title}")
            logger.info(f"Generated {len(video_metadata.hashtags)} hashtags")
            progress['video_metadata'] = True

        except Exception as e:
            logger.warning(f"Video metadata generation failed: {str(e)}")
            logger.warning("Continuing without video metadata...")
    else:
        logger.info("Step 12: Skipping video metadata generation (already completed)")

    # Step 13: Generate metadata for shorts (if not already done)
    if not progress['shorts_metadata_generated'] and progress['shorts_audio_generated']:
        logger.info("Step 13: Generating metadata for shorts")

        short_dirs = get_shorts_directories(story_dir)

        for short_dir in short_dirs:
            # Check if metadata already exists
            metadata_json_path = os.path.join(short_dir, "metadata.json")
            if os.path.exists(metadata_json_path):
                logger.info(f"Metadata already exists for {os.path.basename(short_dir)}, skipping")
                continue

            short = load_short_from_directory(short_dir)
            if short is None:
                logger.warning(f"Could not load short from {short_dir}, skipping metadata generation")
                continue

            # Generate metadata for this short
            try:
                # Get agent-specific configuration
                video_metadata_model, video_metadata_provider = get_agent_config(args, 'video_metadata', model, llm_provider)

                video_metadata_agent = VideoMetadataAgent(
                    verbose=verbose,
                    model=video_metadata_model,
                    provider=video_metadata_provider
                )

                # Create a temporary story object with just this short's content
                temp_story = Story(
                    title=short.title,
                    scenes=[Scene(scene_number=1, narration=short.narration)]
                )

                # Generate metadata using the existing video metadata agent
                metadata = video_metadata_agent.generate_video_metadata(temp_story)

                # Convert to shorts metadata format
                shorts_metadata = ShortsMetadata(
                    short_number=short.short_number,
                    title=metadata.video_title,
                    description=metadata.short_description,
                    keywords=[tag.replace('#', '') for tag in metadata.hashtags[:10]],  # Remove # and limit to 10
                    estimated_duration=f"{short.estimated_duration_seconds} seconds"
                )

                # Save metadata to JSON
                with open(metadata_json_path, 'w', encoding='utf-8') as f:
                    json.dump(shorts_metadata.model_dump(), f, ensure_ascii=False, indent=2)

                logger.info(f"Metadata generated for short {short.short_number}: {shorts_metadata.title}")

            except Exception as e:
                logger.warning(f"Metadata generation failed for short {short.short_number}: {str(e)}")
                continue

        progress['shorts_metadata_generated'] = True
        logger.info("Shorts metadata generation completed")
    else:
        if not progress['shorts_audio_generated']:
            logger.info("Step 13: Skipping shorts metadata generation (shorts audio not generated yet)")
        else:
            logger.info("Step 13: Skipping shorts metadata generation (already completed)")

    # Audio generation pipeline complete
    logger.info(f"Audio generation pipeline completed successfully!")
    logger.info(f"Story directory: {story_dir}")
    logger.info(f"Generated {len(audio_paths)} audio files")


    return story_dir

if __name__ == "__main__":
    try:
        story_dir = main()

        if story_dir:
            print(f"\nSuccess! Real story audio generation pipeline completed.")
            print(f"Story directory: {story_dir}")
            print(f"Generated assets:")
            print(f"  - Research query: {os.path.join(story_dir, 'query.md')}")
            print(f"  - Research report: {os.path.join(story_dir, 'report.json')}")
            print(f"  - Structured report: {os.path.join(story_dir, 'structured_report.json')}")
            print(f"  - Story data: {os.path.join(story_dir, 'story.json')}")
            print(f"  - Dialogue segments: {os.path.join(story_dir, 'dialogue_segments.json')}")
            print(f"  - Video metadata: {os.path.join(story_dir, 'video_metadata.json')}")
            print(f"  - Audio files: {os.path.join(story_dir, 'audio/')}")

            # Show shorts information if available
            short_dirs = get_shorts_directories(story_dir)
            if short_dirs:
                completed_shorts, total_shorts = count_completed_shorts(story_dir)
                print(f"  - Shorts generated: {total_shorts} shorts in individual directories")
                print(f"  - Shorts completed: {completed_shorts}/{total_shorts}")
                for short_dir in short_dirs:
                    short_name = os.path.basename(short_dir)
                    print(f"    * {short_name}/: short.json, segments.json, audio/, metadata.json")
        else:
            print("\nError: Real story audio generation pipeline failed.")

    except Exception as e:
        logger.error(f"Error during execution: {str(e)}", exc_info=True)
        print(f"\nError: {str(e)}")
